package my.com.cmg.rms.controller;

import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemMasterDetailDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemPackagingDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemRouteDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.DrugLabelDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.ItemMasterMethodStatusDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemAtcDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemFrequencyDTO;
import my.com.cmg.rms.dto.AssignRequestDTO;
import my.com.cmg.rms.dto.AssignmentHistoryDTO;
import my.com.cmg.rms.dto.RequestStatusDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.service.IRequestService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/api/v1/rms/request")
public class RequestController {

  private final IRequestService requestService;

  @GetMapping("/list")
  public List<RequestListDTO> getRequestList(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size) {

    PaginationRequestDTO pgDTO = new PaginationRequestDTO(sort, sortDirection, page, size);
    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);

    return requestService.getRequestList(requestDTO, pgDTO);
  }

  @GetMapping("/list/page")
  public PaginationResponseDTO getRequestListPages(
      @RequestParam(required = false) String requestNo,
      @RequestParam(required = false) Long facilitySeqno,
      @RequestParam(required = false) String requestType,
      @RequestParam(required = false) String title,
      @RequestParam(required = false) String assignedTo,
      @RequestParam(required = false) String category,
      @RequestParam(required = false) LocalDate requestDateFrom,
      @RequestParam(required = false) LocalDate requestDateTo,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) Long size) {

    RequestListSearchDTO requestDTO =
        new RequestListSearchDTO(
            requestNo,
            facilitySeqno,
            requestType,
            title,
            assignedTo,
            category,
            requestDateFrom,
            requestDateTo,
            status);

    return requestService.getRequestListPages(requestDTO, size);
  }

  @PostMapping("/saveRequest")
  @ResponseStatus(HttpStatus.CREATED)
  public void saveRequest(@RequestBody SaveRequestDTO dto) {
    requestService.save(dto);
  }

  @GetMapping("/{requestHdrSeqno}")
  public ViewRequestDTO getFullRequest(@PathVariable Long requestHdrSeqno) {
    return requestService.getViewRequest(requestHdrSeqno);
  }

  @PutMapping("/updateRequest/{requestHdrSeqno}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void updateRequest(@PathVariable Long requestHdrSeqno, @RequestBody SaveRequestDTO dto) {
    requestService.update(requestHdrSeqno, dto);
  }

  @PutMapping("/confirmRequest/{requestHdrSeqno}")
  public void confirmRequest(@PathVariable Long requestHdrSeqno) {
    requestService.confirmRequest(requestHdrSeqno);
  }

  @PostMapping("/list/advanceSearch")
  public List<RequestListDTO> searchRequestList(
      @RequestBody RequestListSearchDTO filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) String sortDirection,
      @RequestParam(required = false) Long page,
      @RequestParam(required = false) Long size) {
    PaginationRequestDTO pgDTO = new PaginationRequestDTO(sort, sortDirection, page, size);
    return requestService.getRequestList(filter, pgDTO);
  }

  @GetMapping("/view/{requestHdrSeqno}")
  public ViewRequestDTO viewRequest(@PathVariable Long requestHdrSeqno) {
    return requestService.getViewRequest(requestHdrSeqno);
  }

  @GetMapping("/log/{requestHdrSeqno}")
  public List<AssignmentHistoryDTO> getAssignmentLog(@PathVariable Long requestHdrSeqno) {
    return requestService.getAssignmentLog(requestHdrSeqno);
  }

  @PostMapping("/assignRequest")
  public void assignRequest(@RequestBody AssignRequestDTO dto) {
    requestService.assignRequest(dto);
  }

  @PostMapping("/rejectRequest")
  public void rejectRequest(@RequestBody RequestStatusDTO dto) {
    requestService.rejectRequest(dto);
  }

  @PostMapping("/approveRequest")
  public void approveRequest(@RequestBody RequestStatusDTO dto) {
    requestService.approveRequest(dto);
  }

  @PostMapping("/item-master-detail/save")
  public ResponseEntity<String> saveAdminItemMasterDetail(
      @RequestBody AdminItemMasterDetailDTO dto) {
    requestService.saveAdminItemMasterDetail(dto);
    return ResponseEntity.ok("Item master detail saved successfully.");
  }

  @GetMapping("/admin/item-packaging/{itemSeqno}")
  public ResponseEntity<List<AdminItemPackagingDTO>> getPackagingByItemSeqno(
      @PathVariable Long itemSeqno) {
    return ResponseEntity.ok(requestService.getPackagingByItemSeqno(itemSeqno));
  }

  @PostMapping("/admin/item-packaging")
  public ResponseEntity<Void> saveAllPackaging(@RequestBody List<AdminItemPackagingDTO> dtoList) {
    requestService.saveAllPackaging(dtoList);
    return ResponseEntity.ok().build();
  }

  @PostMapping("/admin/item-route")
  public ResponseEntity<Void> saveItemRoutes(
      @RequestParam Long itemSeqno, @RequestBody List<AdminItemRouteDTO> dtoList) {
    requestService.saveAllItemRoutes(itemSeqno, dtoList);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/admin/item-route/{itemSeqno}")
  public ResponseEntity<List<AdminItemRouteDTO>> getItemRoutes(@PathVariable Long itemSeqno) {
    List<AdminItemRouteDTO> list = requestService.getItemRoutesByItemSeqno(itemSeqno);
    return ResponseEntity.ok(list);
  }

  @GetMapping("/admin/all-item-routes")
  public List<AdminItemRouteDTO> getAllItemRoutes() {
    return requestService.getAllItemRoutes();
  }

  @GetMapping("/item-frequency/{itemSeqno}")
  public ResponseEntity<List<RequestItemFrequencyDTO>> getByItemSeqno(
      @PathVariable Long itemSeqno) {
    return ResponseEntity.ok(requestService.getItemFrequenciesByItemSeqno(itemSeqno));
  }

  @GetMapping("/item-frequency/all-item-routes")
  public List<RequestItemFrequencyDTO> getAllItemFrequencies() {
    return requestService.getAllItemFrequencies();
  }

  @PostMapping("/item-frequency/save-all")
  public ResponseEntity<Void> saveAll(
      @RequestParam Long itemSeqno, @RequestBody List<RequestItemFrequencyDTO> dtoList) {
    requestService.saveAllItemFrequencies(itemSeqno, dtoList);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/item-atc/{itemSeqno}")
  public ResponseEntity<List<RequestItemAtcDTO>> getItemAtcByItemSeqno(
      @PathVariable Long itemSeqno) {
    return ResponseEntity.ok(requestService.getItemAtcByItemSeqno(itemSeqno));
  }

  @GetMapping("/item-atc/all-item-atc")
  public List<RequestItemAtcDTO> getAllItemAtc() {
    return requestService.getAllItemAtc();
  }

  @PostMapping("/item-atc/save-all")
  public ResponseEntity<Void> saveAllItemAtc(
      @RequestParam Long itemSeqno, @RequestBody List<RequestItemAtcDTO> dtoList) {
    requestService.saveAllItemAtc(itemSeqno, dtoList);
    return ResponseEntity.ok().build();
  }

  @GetMapping("/drug-label/{itemSeqno}")
  public ResponseEntity<List<DrugLabelDTO>> getDrugLabelByItemSeqno(@PathVariable Long itemSeqno) {
    return ResponseEntity.ok(requestService.getDrugLabelByItemSeqno(itemSeqno));
  }

  @GetMapping("/drug-label/all-drug-label")
  public List<DrugLabelDTO> getAllDrugLabel() {
    return requestService.getAllDrugLabel();
  }

  @PostMapping("/drug-label/save-all")
  public ResponseEntity<Void> saveAllDrugLabel(
      @RequestParam Long itemSeqno, @RequestBody List<DrugLabelDTO> dtoList) {
    requestService.saveAllDrugLabel(itemSeqno, dtoList);
    return ResponseEntity.ok().build();
  }

  @PutMapping("/item-master-method-status/update")
  public ResponseEntity<Void> updateItemMasterMethodStatus(
      @RequestBody ItemMasterMethodStatusDTO dto) {
    requestService.updateItemMasterMethodStatus(dto);
    return ResponseEntity.ok().build();
  }
}
