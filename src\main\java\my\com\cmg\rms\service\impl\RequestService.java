package my.com.cmg.rms.service.impl;

import static my.com.cmg.rms.constant.RmsConstant.*;

import jakarta.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemMasterDetailDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemPackagingDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.AdminItemRouteDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.DrugLabelDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.ItemMasterMethodStatusDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemAtcDTO;
import my.com.cmg.rms.dto.AdminItemMasterDetails.RequestItemFrequencyDTO;
import my.com.cmg.rms.dto.AssignRequestDTO;
import my.com.cmg.rms.dto.AssignmentHistoryDTO;
import my.com.cmg.rms.dto.RequestDtlDTO;
import my.com.cmg.rms.dto.RequestHeaderDTO;
import my.com.cmg.rms.dto.RequestHeaderDtlDTO;
import my.com.cmg.rms.dto.RequestStatusDTO;
import my.com.cmg.rms.dto.paging.PaginationRequestDTO;
import my.com.cmg.rms.dto.paging.PaginationResponseDTO;
import my.com.cmg.rms.dto.requestDetail.FacilityMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemMasterDetailDTO;
import my.com.cmg.rms.dto.requestDetail.ItemPackagingDetailDTO;
import my.com.cmg.rms.dto.requestDetail.SaveRequestDTO;
import my.com.cmg.rms.dto.requestDetail.SupplierMasterDetailDTO;
import my.com.cmg.rms.dto.requestListing.RequestListDTO;
import my.com.cmg.rms.dto.requestListing.RequestListSearchDTO;
import my.com.cmg.rms.dto.viewDetail.FacilityMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ItemPackagingRequestDTO;
import my.com.cmg.rms.dto.viewDetail.SupplierMasterRequestDTO;
import my.com.cmg.rms.dto.viewDetail.ViewRequestDTO;
import my.com.cmg.rms.exception.ExceptionCode;
import my.com.cmg.rms.exception.RmsException;
import my.com.cmg.rms.mapper.RequestMapper;
import my.com.cmg.rms.model.RequestAssignmentHistory;
import my.com.cmg.rms.model.RequestDrugLabel;
import my.com.cmg.rms.model.RequestDtl;
import my.com.cmg.rms.model.RequestFacility;
import my.com.cmg.rms.model.RequestHdr;
import my.com.cmg.rms.model.RequestItemAtc;
import my.com.cmg.rms.model.RequestItemFrequency;
import my.com.cmg.rms.model.RequestItemMaster;
import my.com.cmg.rms.model.RequestItemPackaging;
import my.com.cmg.rms.model.RequestItemRoute;
import my.com.cmg.rms.model.RequestSupplier;
import my.com.cmg.rms.repository.jooq.RequestRepositoryJooq;
import my.com.cmg.rms.repository.jpa.RequestAssignmentHistoryRepository;
import my.com.cmg.rms.repository.jpa.RequestDrugLabelRepository;
import my.com.cmg.rms.repository.jpa.RequestDtlRepository;
import my.com.cmg.rms.repository.jpa.RequestFacilityRepository;
import my.com.cmg.rms.repository.jpa.RequestHdrRepository;
import my.com.cmg.rms.repository.jpa.RequestItemAtcRepository;
import my.com.cmg.rms.repository.jpa.RequestItemFrequencyRepository;
import my.com.cmg.rms.repository.jpa.RequestItemMasterRepository;
import my.com.cmg.rms.repository.jpa.RequestItemPackagingRepository;
import my.com.cmg.rms.repository.jpa.RequestItemRouteRepository;
import my.com.cmg.rms.repository.jpa.RequestSupplierRepository;
import my.com.cmg.rms.security.CurrentUserUtil;
import my.com.cmg.rms.service.IRequestService;
import my.com.cmg.rms.utils.PaginationUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class RequestService implements IRequestService {

  private final RequestRepositoryJooq requestRepositoryJooq;
  private final RequestHdrRepository requestHdrRepository;
  private final RequestDtlRepository requestDtlRepository;
  private final RequestSupplierRepository requestSupplierRepository;
  private final RequestFacilityRepository requestFacilityRepository;
  private final RequestItemMasterRepository requestItemMasterRepository;
  private final RequestItemPackagingRepository requestItemPackagingRepository;
  private final RequestItemRouteRepository requestItemRouteRepository;
  private final RequestItemFrequencyRepository requestItemFrequencyRepository;
  private final RequestItemAtcRepository requestItemAtcRepository;
  private final RequestAssignmentHistoryRepository requestAssignmentHistoryRepository;
  private final RequestDrugLabelRepository requestDrugLabelRepository;

  @Override
  @Transactional
  public Long save(SaveRequestDTO dto) {
    RequestHdr hdr = saveRequestHeader(dto.requestHeaderDtl());
    String requestType = dto.requestHeaderDtl().requestType();
    String subCategory = dto.requestHeaderDtl().subCategory();

    boolean isNew = DATA_REQUEST_TYPE_NEW.toString().equalsIgnoreCase(requestType);
    boolean isUpdate = DATA_REQUEST_TYPE_UPDATE.toString().equalsIgnoreCase(requestType);

    if (isUpdate && dto.requestDtl() != null) {
      Long transSeqno = saveRequestDetail(dto.requestDtl(), hdr);
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr, transSeqno);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr, transSeqno);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr, transSeqno);
      }
    }

    if (isNew) {
      Long transSeqno = null;
      if (dto.requestDtl() != null) {
        transSeqno = saveRequestDetail(dto.requestDtl(), hdr);
      }
      if (DATA_REQUEST_SUB_CATEGORY_NEW_SUPPLIER_MASTER.equalsIgnoreCase(subCategory)) {
        saveSupplier(dto.supplierMaster(), hdr, transSeqno);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_MASTER.equalsIgnoreCase(subCategory)) {
        saveItem(dto.itemMaster(), hdr, transSeqno);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_ITEM_PACKAGING.equalsIgnoreCase(subCategory)) {
        savePackaging(dto.itemPackaging(), hdr, transSeqno);
      } else if (DATA_REQUEST_SUB_CATEGORY_NEW_FACILITY_MASTER.equalsIgnoreCase(subCategory)) {
        saveFacility(dto.facilityMaster(), hdr, transSeqno);
      }
    }

    return hdr.getRequestHdrSeqno();
  }

  private String trim(String input, int maxLength) {
    if (input == null) return null;
    return input.length() > maxLength ? input.substring(0, maxLength) : input;
  }

  @Override
  @Transactional
  public void update(Long requestHdrSeqno, SaveRequestDTO dto) {
    RequestHdr existingHdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(null, null));

    RequestHeaderDtlDTO headerDTO = dto.requestHeaderDtl();
    existingHdr.setTitle(headerDTO.title());
    existingHdr.setReference(headerDTO.reference());
    existingHdr.setIntention(headerDTO.intention());
    existingHdr.setReason(headerDTO.reason());
    existingHdr.setUpdatedBy(1L);
    existingHdr.setStatus("DRAFT");
    existingHdr.setUpdatedDate(LocalDateTime.now());
    requestHdrRepository.save(existingHdr);

    List<RequestDtl> dtls = requestDtlRepository.findByRequestHdrRequestHdrSeqno(requestHdrSeqno);

    if (!dtls.isEmpty() && dto.requestDtl() != null) {
      RequestDtl existingDtl = dtls.get(0);
      RequestDtlDTO dtlDTO = dto.requestDtl();
      existingDtl.setTransDetails(dtlDTO.transDetails());
      existingDtl.setUpdatedBy(1L);
      existingDtl.setUpdatedDate(LocalDateTime.now());
      requestDtlRepository.save(existingDtl);
    }
  }

  @Override
  @Transactional
  public void confirmRequest(Long requestHdrSeqno) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(requestHdrSeqno)
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    if (!"DRAFT".equalsIgnoreCase(hdr.getStatus())) {
      throw new RmsException(ExceptionCode.ERROR, "Only draft requests can be confirmed");
    }

    hdr.setStatus("PENDING");
    hdr.setUpdatedBy(1L);
    hdr.setUpdatedDate(LocalDateTime.now());

    requestHdrRepository.save(hdr);
  }

  private RequestHdr saveRequestHeader(RequestHeaderDtlDTO headerDtlDTO) {
    RequestHdr entity = new RequestHdr();
    entity.setRequestType(headerDtlDTO.requestType());
    entity.setCategory(headerDtlDTO.category());
    entity.setSubCategory(headerDtlDTO.subCategory());
    entity.setTitle(headerDtlDTO.title());
    entity.setReference(headerDtlDTO.reference());
    entity.setIntention(headerDtlDTO.intention());
    entity.setReason(headerDtlDTO.reason());

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setStatus("DRAFT");
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setFacilitySeqno(1L);
    entity.setRequestedBySeqno(1L);
    entity.setRequestedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    String requestNo = UUID.randomUUID().toString().substring(0, 10);

    log.info("Generated requestNo: {}", requestNo);
    entity.setRequestNo(requestNo);

    return requestHdrRepository.save(entity);
  }

  private Long saveRequestDetail(RequestDtlDTO dtlDTO, RequestHdr hdr) {
    Long maxTransSeqno =
        requestDtlRepository.findMaxTransSeqnoByRequestHdr(hdr.getRequestHdrSeqno()).orElse(0L);

    Long nextSeqno = maxTransSeqno + 1;

    RequestDtl entity = new RequestDtl();
    entity.setRequestHdr(hdr);
    entity.setTransSeqno(nextSeqno);
    entity.setTransCode(dtlDTO.transCode());
    entity.setTransName(dtlDTO.transName());
    entity.setTransType(dtlDTO.transType());
    entity.setTransDetails(dtlDTO.transDetails());
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    requestDtlRepository.save(entity);
    return nextSeqno;
  }

  private void saveSupplier(SupplierMasterDetailDTO dto, RequestHdr hdr, Long transSeqno) {
    RequestSupplier entity = new RequestSupplier();

    entity.setSupplierReqCode("SUP-" + UUID.randomUUID().toString().substring(0, 8));
    entity.setSupplierReqName(trim(dto.supplierReqName(), 100));
    entity.setSupplierReqType(trim(dto.supplierReqType(), 10));
    entity.setSupplierReqTypeDesc(trim(dto.supplierReqType(), 100));
    entity.setCompanyRegNo(trim(dto.companyRegNo(), 20));

    if (dto.regExpiryDate() != null) {
      entity.setRegExpiryDate(dto.regExpiryDate().atStartOfDay());
    }

    entity.setTrsRegNo(trim(dto.trsRegNo(), 20));
    entity.setCompanyStatus(trim(dto.companyStatus(), 10));
    entity.setCompanyStatusDesc(trim(dto.companyStatus(), 100));

    entity.setAddress1(trim(dto.address1(), 100));
    entity.setAddress2(trim(dto.address2(), 100));
    entity.setAddress3(trim(dto.address3(), 100));
    entity.setCity(trim(dto.city(), 50));
    entity.setState(trim(dto.state(), 10));
    entity.setPostcode(trim(dto.postcode(), 10));
    entity.setCountry(trim(dto.country(), 50));

    entity.setTransSeqno(transSeqno);
    entity.setMobilePhone(trim(dto.mobilePhone(), 20));
    entity.setEmail(trim(dto.email(), 100));
    entity.setContactPerson(trim(dto.contactPerson(), 100));
    entity.setContactNo(trim(dto.contactNo(), 20));

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    requestSupplierRepository.save(entity);
  }

  private void saveItem(ItemMasterDetailDTO dto, RequestHdr hdr, Long transSeqno) {
    RequestItemMaster entity = new RequestItemMaster();

    entity.setItemReqCode("ITM-" + UUID.randomUUID().toString().substring(0, 8));
    entity.setItemGroupCode(dto.itemGroupCode() != null ? trim(dto.itemGroupCode(), 20) : "");
    entity.setGenericNameSeqno(dto.genericNameSeqno());
    entity.setOtherActiveIngredient(trim(dto.otherActiveIngredient(), 100));

    if (dto.strength() != null && !dto.strength().isBlank()) {
      entity.setStrength(new BigDecimal(dto.strength()));
    }

    entity.setDosageSeqno(dto.dosageSeqno());
    entity.setDosageCode(dto.dosageCode() != null ? trim(dto.dosageCode(), 20) : "");
    entity.setItemName(trim(dto.itemName(), 100));
    entity.setItemCategorySeqno(dto.itemCatSeqno());
    entity.setItemCategoryCode(
        dto.itemCategoryCode() != null ? trim(dto.itemCategoryCode(), 20) : "");
    entity.setItemSubgroupSeqno(dto.itemSubgroupSeqno());
    entity.setItemSubgroupCode(
        dto.itemSubgroupCode() != null ? trim(dto.itemSubgroupCode(), 20) : "");
    entity.setRpItemTypeCode(trim(dto.rpItemTypeCode(), 10));
    entity.setRpItemTypeDesc(trim(dto.rpItemTypeDesc(), 100));

    entity.setItemPackagingSeqno(dto.itemPackagingSeqno());
    entity.setItemPackagingCode(
        dto.itemPackagingCode() != null ? trim(dto.itemPackagingCode(), 20) : "");
    entity.setItemPackagingName(trim(dto.itemPackagingName(), 100));

    entity.setSkuSeqno(dto.skuSeqno());
    entity.setSkuAbbr(dto.skuAbbr() != null ? trim(dto.skuAbbr(), 10) : "");
    entity.setPkuSeqno(dto.pkuSeqno());
    entity.setPkuAbbr(dto.pkuAbbr() != null ? trim(dto.pkuAbbr(), 10) : "");

    entity.setMdcNo(trim(dto.mdcNo(), 30));
    entity.setProductSeqno(dto.productSeqno());
    entity.setProductName(trim(dto.productName(), 100));
    entity.setManufacturedName(trim(dto.manufacturedName(), 100));
    entity.setImporterName(trim(dto.importerName(), 100));
    entity.setManufacturedAddress(trim(dto.manufacturedAddress(), 200));
    entity.setImporterAddress(trim(dto.importerAddress(), 200));
    entity.setGtinNo(trim(dto.gtinNo(), 30));
    entity.setMdaNo(trim(dto.mdaNo(), 30));

    if (dto.conversionFactorNum() != null) {
      entity.setConversionFactorNum(dto.conversionFactorNum());
    }

    entity.setPackagingDesc(trim(dto.packagingDesc(), 200));
    entity.setTransSeqno(transSeqno);

    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    requestItemMasterRepository.save(entity);
  }

  private void savePackaging(ItemPackagingDetailDTO dto, RequestHdr hdr, Long transSeqno) {
    RequestItemPackaging entity = new RequestItemPackaging();

    entity.setItemSeqno(dto.itemSeqno());
    entity.setItemName(trim(dto.itemName(), 100));
    entity.setItemCode(dto.itemCode() != null ? trim(dto.itemCode(), 20) : "");
    entity.setItemPackagingReqCode(
        dto.itemPackagingReqCode() != null ? trim(dto.itemPackagingReqCode(), 20) : "");
    entity.setItemPackagingName(trim(dto.itemPackagingName(), 100));

    entity.setSkuSeqno(dto.skuSeqno() != null ? dto.skuSeqno() : 0L);
    entity.setSkuAbbr(dto.skuAbbr() != null ? trim(dto.skuAbbr(), 10) : "");
    entity.setPkuSeqno(dto.pkuSeqno() != null ? dto.pkuSeqno() : 0L);
    entity.setPkuAbbr(dto.pkuAbbr() != null ? trim(dto.pkuAbbr(), 10) : "");

    if (dto.conversionFactor() != null && !dto.conversionFactor().isBlank()) {
      entity.setConversionFactor(new BigDecimal(dto.conversionFactor()));
    }

    entity.setPackagingDesc(trim(dto.packagingDesc(), 200));
    entity.setProductList(trim(dto.productList(), 200));
    entity.setProductSeqno(dto.productSeqno() != null ? dto.productSeqno() : 0L);
    entity.setProductName(trim(dto.productName(), 100));
    entity.setManufacturedName(trim(dto.manufacturedName(), 100));
    entity.setImporterName(trim(dto.importerName(), 100));
    entity.setManufacturedAddress(trim(dto.manufacturedAddress(), 200));
    entity.setImporterAddress(trim(dto.importerAddress(), 200));
    entity.setGtinNo(trim(dto.gtinNo(), 30));
    entity.setMdaNo(trim(dto.mdaNo(), 30));

    entity.setTransSeqno(transSeqno);
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    requestItemPackagingRepository.save(entity);
  }

  private void saveFacility(FacilityMasterDetailDTO dto, RequestHdr hdr, Long transSeqno) {
    RequestFacility entity = new RequestFacility();

    entity.setFacilityReqName(trim(dto.facilityReqName(), 100));
    entity.setFacilityReqGroup(trim(dto.facilityReqGroup(), 50));
    entity.setMinistry(trim(dto.ministry(), 100));
    entity.setFacilityReqCategory(trim(dto.facilityReqCategory(), 50));
    entity.setFacilityReqType(trim(dto.facilityReqType(), 50));

    entity.setAddress1(trim(dto.address1(), 100));
    entity.setAddress2(trim(dto.address2(), 100));
    entity.setAddress3(trim(dto.address3(), 100));
    entity.setCity(trim(dto.city(), 50));
    entity.setState(trim(dto.state(), 10));
    entity.setPostcode(trim(dto.postcode(), 10));
    entity.setCountry(trim(dto.country(), 50));

    entity.setMobilePhone(trim(dto.mobilePhone(), 20));
    entity.setEmail(trim(dto.email(), 100));
    entity.setContactPerson(trim(dto.contactPerson(), 100));
    entity.setContactNo(trim(dto.contactNo(), 20));

    entity.setTransSeqno(transSeqno);
    entity.setCreatedBy(1L);
    entity.setUpdatedBy(1L);
    entity.setCreatedDate(LocalDateTime.now());
    entity.setUpdatedDate(LocalDateTime.now());
    entity.setActiveFlag('A');

    requestFacilityRepository.save(entity);
  }

  @Override
  public List<RequestListDTO> getRequestList(
      RequestListSearchDTO requestDTO, PaginationRequestDTO pgDTO) {
    PaginationRequestDTO pg = PaginationUtil.pageSorting(pgDTO, new RequestMapper(), true);
    String userDivision = CurrentUserUtil.getCurrentUserDivision();
    boolean isAdmin = CurrentUserUtil.isAdmin();
    return requestRepositoryJooq.getRequestList(requestDTO, pg, userDivision, isAdmin);
  }

  @Override
  public PaginationResponseDTO getRequestListPages(RequestListSearchDTO requestDTO, Long size) {
    Long pgSize = requestRepositoryJooq.getRequestListCount(requestDTO);
    return PaginationUtil.pagination(size, pgSize);
  }

  @Override
  public ViewRequestDTO getViewRequest(Long requestHdrSeqno) {
    RequestHeaderDTO header = requestRepositoryJooq.getRequestHeader(requestHdrSeqno);
    if (header == null) return null;

    RequestDtlDTO[] details = header.requestDetails();
    if (details == null || details.length == 0)
      return new ViewRequestDTO(header, null, null, null, null, null);

    String transType = header.requestDetails()[0].transType();
    Long transSeqno = header.requestDetails()[0].transSeqno();

    ItemMasterRequestDTO item = null;
    ItemPackagingRequestDTO packaging = null;
    FacilityMasterRequestDTO facility = null;
    SupplierMasterRequestDTO supplier = null;

    if ("IM".equalsIgnoreCase(transType)) {
      item = requestRepositoryJooq.getItemMasterRequest(transSeqno);
    } else if ("IP".equalsIgnoreCase(transType)) {
      packaging = requestRepositoryJooq.getItemPackagingRequest(transSeqno);
    } else if ("FM".equalsIgnoreCase(transType)) {
      facility = requestRepositoryJooq.getFacilityRequest(transSeqno);
    } else if ("SM".equalsIgnoreCase(transType)) {
      supplier = requestRepositoryJooq.getSupplierRequest(transSeqno);
    }

    return new ViewRequestDTO(header, details, item, packaging, facility, supplier);
  }

  public List<RequestListDTO> searchRequests(
      RequestListSearchDTO filter, PaginationRequestDTO pgDTO) {
    String division = CurrentUserUtil.getCurrentUserDivision();
    boolean isAdmin = CurrentUserUtil.isAdmin();
    return requestRepositoryJooq.getRequestList(filter, pgDTO, division, isAdmin);
  }

  @Override
  @Transactional
  public void assignRequest(AssignRequestDTO dto) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(dto.requestHdrSeqno())
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    hdr.setAssignedTo(dto.assignedToDivision());
    hdr.setUpdatedDate(LocalDateTime.now());
    hdr.setUpdatedBy(CurrentUserUtil.getCurrentUserId());
    requestHdrRepository.save(hdr);

    RequestAssignmentHistory history = new RequestAssignmentHistory();
    history.setRequestHdr(hdr);
    history.setAssignedTo(dto.assignedToDivision());
    history.setRemarks(dto.remarks());
    history.setAssignedBy(CurrentUserUtil.getCurrentUserId());
    history.setAssignedDate(LocalDateTime.now());

    history.setCreatedBy(CurrentUserUtil.getCurrentUserId());
    history.setCreatedDate(LocalDateTime.now());
    history.setUpdatedBy(CurrentUserUtil.getCurrentUserId());
    history.setUpdatedDate(LocalDateTime.now());

    requestAssignmentHistoryRepository.save(history);
  }

  @Override
  @Transactional
  public void rejectRequest(RequestStatusDTO dto) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(dto.requestHdrSeqno())
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    hdr.setStatus("REJECTED");
    hdr.setUpdatedDate(LocalDateTime.now());
    hdr.setUpdatedBy(CurrentUserUtil.getCurrentUserId());
    requestHdrRepository.save(hdr);
  }

  @Override
  @Transactional
  public void approveRequest(RequestStatusDTO dto) {
    RequestHdr hdr =
        requestHdrRepository
            .findById(dto.requestHdrSeqno())
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Request not found"));

    hdr.setStatus("APPROVED");
    hdr.setUpdatedDate(LocalDateTime.now());
    hdr.setUpdatedBy(CurrentUserUtil.getCurrentUserId());
    requestHdrRepository.save(hdr);
  }

  @Override
  public List<AssignmentHistoryDTO> getAssignmentLog(Long requestHdrSeqno) {
    List<RequestAssignmentHistory> histories =
        requestAssignmentHistoryRepository.findByRequestHdr_RequestHdrSeqnoOrderByAssignedDateDesc(
            requestHdrSeqno);

    return histories.stream()
        .map(
            history ->
                new AssignmentHistoryDTO(
                    history.getAssignedDate(),
                    history.getAssignedTo(),
                    history.getRemarks(),
                    String.valueOf(history.getAssignedBy())))
        .toList();
  }

  @Override
  @Transactional
  public void saveAdminItemMasterDetail(AdminItemMasterDetailDTO dto) {
    RequestItemMaster item =
        requestItemMasterRepository
            .findById(dto.getItemSeqno())
            .orElseThrow(() -> new RmsException(ExceptionCode.ERROR, "Item master not found"));

    item.setItemGroupCode(dto.getItemGroupCode());
    item.setItemGroupDesc(dto.getItemGroupDesc());
    item.setItemCategorySeqno(dto.getItemCatSeqno());
    item.setItemCategoryCode(dto.getItemCategoryCode());
    item.setCategoryDesc(dto.getCategoryDesc());
    item.setItemSubgroupSeqno(dto.getItemSubgroupSeqno());
    item.setItemSubgroupCode(dto.getItemSubgroupCode());
    item.setSubgroupDesc(dto.getSubgroupDesc());
    item.setSkuSeqno(dto.getSkuSeqno());
    item.setSkuAbbr(dto.getSkuAbbr());
    item.setItemNameAdmin(dto.getItemNameAdmin());
    item.setVoteObject(dto.getVoteObject());
    item.setItemDispensable(dto.getItemDispensable());
    item.setDrugLabel(dto.getDrugLabel());
    item.setInstructionSeqno(dto.getInstructionSeqno());
    item.setInstruction(dto.getInstruction());
    item.setSpecialInstructionSeqno(dto.getSpecialInstructionSeqno());
    item.setSpecialInstruction(dto.getSpecialInstruction());
    item.setCautionarySeqno(dto.getCautionarySeqno());
    item.setCautionary(dto.getCautionary());
    item.setLocalLang1(dto.getLocalLang1());
    item.setLocalLang2(dto.getLocalLang2());
    item.setLocalLang3(dto.getLocalLang3());
    item.setStrengthValue(dto.getStrengthValue());
    item.setStrengthUnit(dto.getStrengthUnit());
    item.setSnomedCtCode(dto.getSnomedCtCode());
    item.setSnomedCtName(dto.getSnomedCtName());
    item.setSnomedCtSystem(dto.getSnomedCtSystem());
    item.setQuantity(dto.getQuantity());
    item.setDosageGroupCode(dto.getDosageGroupCode());
    item.setDosageGroupDesc(dto.getDosageGroupDesc());
    item.setDrugTypeCode(dto.getDrugTypeCode());
    item.setDrugTypeDesc(dto.getDrugTypeDesc());
    item.setSpecialOrderConfiguration(dto.getSpecialOrderConfiguration());
    item.setDrugSchedule(dto.getDrugSchedule());
    item.setDrugScheduleNew(dto.getDrugScheduleNew());
    item.setDoseToBeDisplayed(dto.getDoseToBeDisplayed());
    item.setNationalEssentialMedicine(dto.getNationalEssentialMedicine());
    item.setMedicationAssistedTheraphy(dto.getMedicationAssistedTheraphy());
    item.setNonCommunicableDisease(dto.getNonCommunicableDisease());
    item.setBiologicalProductLotRelease(dto.getBiologicalProductLotRelease());
    item.setUpdatedBy(CurrentUserUtil.getCurrentUserId());
    item.setUpdatedDate(LocalDateTime.now());

    requestItemMasterRepository.save(item);
  }

  @Override
  public List<AdminItemPackagingDTO> getPackagingByItemSeqno(Long itemSeqno) {
    List<RequestItemPackaging> list = requestItemPackagingRepository.findByItemSeqno(itemSeqno);
    return list.stream()
        .map(
            entity -> {
              AdminItemPackagingDTO dto = new AdminItemPackagingDTO();
              BeanUtils.copyProperties(entity, dto);
              return dto;
            })
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public void saveAllPackaging(List<AdminItemPackagingDTO> dtoList) {
    List<RequestItemPackaging> entities = new ArrayList<>();
    Long currentUserId = CurrentUserUtil.getCurrentUserId();
    LocalDateTime now = LocalDateTime.now();

    for (AdminItemPackagingDTO dto : dtoList) {
      RequestItemPackaging entity = new RequestItemPackaging();

      entity.setItemSeqno(dto.getItemSeqno());
      entity.setItemCode(dto.getItemCode());
      entity.setItemPackagingReqCode(dto.getItemPackagingReqCode());
      entity.setItemPackagingName(dto.getItemPackagingName());
      entity.setMalRegistrationNo(dto.getMalRegistrationNo());
      entity.setSkuAbbr(dto.getSkuAbbr());
      entity.setSkuSeqno(dto.getSkuSeqno());
      entity.setPkuSeqno(dto.getPkuSeqno());
      entity.setPkuAbbr(dto.getPkuAbbr());
      if (dto.getConversionFactor() != null)
        entity.setConversionFactor(BigDecimal.valueOf(dto.getConversionFactor()));
      entity.setPurchaseTypeKonsesi(dto.getPurchaseTypeKonsesi());
      entity.setPurchaseTypePembelianTerus(dto.getPurchaseTypePembelianTerus());
      entity.setPurchaseTypeTenderPusat(dto.getPurchaseTypeTenderPusat());
      entity.setItemCodeForKonsesi(dto.getItemCodeForKonsesi());

      entity.setCreatedBy(currentUserId);
      entity.setUpdatedBy(currentUserId);
      entity.setCreatedDate(now);
      entity.setUpdatedDate(now);

      entities.add(entity);
    }

    requestItemPackagingRepository.saveAll(entities);
  }

  @Override
  @Transactional
  public void saveAllItemRoutes(Long itemSeqno, List<AdminItemRouteDTO> dtoList) {
    Long currentUserId = CurrentUserUtil.getCurrentUserId();
    LocalDateTime now = LocalDateTime.now();

    List<RequestItemRoute> entities =
        dtoList.stream()
            .map(
                dto -> {
                  RequestItemRoute entity = new RequestItemRoute();

                  entity.setItemRouteReqSeqno(dto.itemRouteReqSeqno());
                  entity.setItemSeqno(itemSeqno);
                  entity.setRouteCode(dto.routeCode());
                  entity.setRouteDescription(dto.routeDescription());
                  entity.setRouteFactor(dto.routeFactor());
                  entity.setLocalDescription(dto.localDescription());
                  entity.setStatus(dto.status());

                  if (dto.itemRouteReqSeqno() == null) {
                    entity.setCreatedBy(currentUserId);
                    entity.setCreatedDate(now);
                    entity.setActiveFlag('A');
                  } else {
                    RequestItemRoute existing =
                        requestItemRouteRepository
                            .findById(dto.itemRouteReqSeqno())
                            .orElseThrow(
                                () ->
                                    new RmsException(
                                        ExceptionCode.ERROR,
                                        "Item route not found with seqno "
                                            + dto.itemRouteReqSeqno()));

                    entity.setCreatedBy(existing.getCreatedBy());
                    entity.setCreatedDate(existing.getCreatedDate());
                  }

                  entity.setUpdatedBy(currentUserId);
                  entity.setUpdatedDate(now);

                  return entity;
                })
            .collect(Collectors.toList());

    requestItemRouteRepository.saveAll(entities);
  }

  @Override
  @Transactional
  public List<AdminItemRouteDTO> getItemRoutesByItemSeqno(Long itemSeqno) {
    List<RequestItemRoute> list = requestItemRouteRepository.findByItemSeqno(itemSeqno);
    return list.stream()
        .map(
            entity -> {
              return new AdminItemRouteDTO(
                  entity.getItemRouteReqSeqno(),
                  entity.getItemSeqno(),
                  entity.getRouteCode(),
                  entity.getRouteDescription(),
                  entity.getRouteFactor(),
                  entity.getLocalDescription(),
                  entity.getStatus());
            })
        .collect(Collectors.toList());
  }

  @Override
  public List<AdminItemRouteDTO> getAllItemRoutes() {
    List<RequestItemRoute> routes = requestItemRouteRepository.findAll();
    return routes.stream().map(this::convertToAdminItemRouteDTO).collect(Collectors.toList());
  }

  private AdminItemRouteDTO convertToAdminItemRouteDTO(RequestItemRoute entity) {
    entity.setItemRouteReqSeqno(entity.getItemRouteReqSeqno());
    entity.setItemSeqno(entity.getItemSeqno());
    entity.setRouteCode(entity.getRouteCode());
    entity.setRouteDescription(entity.getRouteDescription());
    entity.setRouteFactor(entity.getRouteFactor());
    entity.setLocalDescription(entity.getLocalDescription());
    entity.setStatus(entity.getStatus());
    return new AdminItemRouteDTO(
        entity.getItemRouteReqSeqno(),
        entity.getItemSeqno(),
        entity.getRouteCode(),
        entity.getRouteDescription(),
        entity.getRouteFactor(),
        entity.getLocalDescription(),
        entity.getStatus());
  }

  @Override
  public List<RequestItemFrequencyDTO> getItemFrequenciesByItemSeqno(Long itemSeqno) {
    List<RequestItemFrequency> list = requestItemFrequencyRepository.findByItemSeqno(itemSeqno);
    return list.stream()
        .map(
            entity -> {
              return new RequestItemFrequencyDTO(
                  entity.getFrequencyReqSeqno(),
                  entity.getItemSeqno(),
                  entity.getFrequencyCode(),
                  entity.getFrequencyDesc(),
                  entity.getFrequencyLocalDesc(),
                  entity.getGuideline(),
                  entity.getStatus());
            })
        .collect(Collectors.toList());
  }

  @Override
  public List<RequestItemFrequencyDTO> getAllItemFrequencies() {
    List<RequestItemFrequency> list = requestItemFrequencyRepository.findAll();
    return list.stream()
        .map(
            entity -> {
              return new RequestItemFrequencyDTO(
                  entity.getFrequencyReqSeqno(),
                  entity.getItemSeqno(),
                  entity.getFrequencyCode(),
                  entity.getFrequencyDesc(),
                  entity.getFrequencyLocalDesc(),
                  entity.getGuideline(),
                  entity.getStatus());
            })
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public void saveAllItemFrequencies(Long itemSeqno, List<RequestItemFrequencyDTO> dtoList) {
    List<RequestItemFrequency> entities = new ArrayList<>();
    Long currentUserId = CurrentUserUtil.getCurrentUserId();
    LocalDateTime now = LocalDateTime.now();

    for (RequestItemFrequencyDTO dto : dtoList) {
      RequestItemFrequency entity = new RequestItemFrequency();

      entity.setFrequencyReqSeqno(dto.frequencyReqSeqno());
      entity.setItemSeqno(itemSeqno);
      entity.setFrequencyCode(dto.frequencyCode());
      entity.setFrequencyDesc(dto.frequencyDesc());
      entity.setFrequencyLocalDesc(dto.frequencyLocalDesc());
      entity.setGuideline(dto.guideline());
      entity.setStatus(dto.status());
      if (dto.frequencyReqSeqno() == null) {
        entity.setCreatedBy(currentUserId);
        entity.setCreatedDate(now);
        entity.setActiveFlag('A');
      } else {
        RequestItemFrequency existing =
            requestItemFrequencyRepository
                .findById(dto.frequencyReqSeqno())
                .orElseThrow(
                    () ->
                        new RmsException(
                            ExceptionCode.ERROR,
                            "Frequency not found with seqno " + dto.frequencyReqSeqno()));

        entity.setCreatedBy(existing.getCreatedBy());
        entity.setCreatedDate(existing.getCreatedDate());
      }
      entity.setUpdatedBy(currentUserId);
      entity.setUpdatedDate(now);

      entities.add(entity);
    }

    requestItemFrequencyRepository.saveAll(entities);
  }

  @Override
  public List<RequestItemAtcDTO> getItemAtcByItemSeqno(Long itemSeqno) {
    return requestRepositoryJooq.getItemAtcListByItemSeqno(itemSeqno);
  }

  @Override
  public List<RequestItemAtcDTO> getAllItemAtc() {
    List<RequestItemAtc> list = requestItemAtcRepository.findAll();
    return list.stream()
        .map(
            entity -> {
              return new RequestItemAtcDTO(
                  entity.getAtcReqSeqno(),
                  entity.getItemSeqno(),
                  entity.getAtcReqCode(),
                  entity.getAtcReqName(),
                  entity.getAtcReqDesc(),
                  entity.getAtcDefinedDailyDose(),
                  entity.getStatus());
            })
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public void saveAllItemAtc(Long itemSeqno, List<RequestItemAtcDTO> dtoList) {
    List<RequestItemAtc> entities = new ArrayList<>();
    Long currentUserId = CurrentUserUtil.getCurrentUserId();
    LocalDateTime now = LocalDateTime.now();

    for (RequestItemAtcDTO dto : dtoList) {
      RequestItemAtc entity = new RequestItemAtc();

      entity.setAtcReqSeqno(dto.atcReqSeqno());
      entity.setItemSeqno(itemSeqno);
      entity.setAtcReqCode(dto.atcReqCode());
      entity.setAtcReqName(dto.atcReqName());
      entity.setAtcReqDesc(dto.atcReqDesc());
      entity.setAtcDefinedDailyDose(dto.atcDefinedDailyDose());
      entity.setStatus(dto.status());

      if (dto.atcReqSeqno() == null) {
        entity.setCreatedBy(currentUserId);
        entity.setCreatedDate(now);
        entity.setActiveFlag('A');
      } else {
        RequestItemAtc existing =
            requestItemAtcRepository
                .findById(dto.atcReqSeqno())
                .orElseThrow(
                    () ->
                        new RmsException(
                            ExceptionCode.ERROR, "ATC not found with seqno " + dto.atcReqSeqno()));

        entity.setCreatedBy(existing.getCreatedBy());
        entity.setCreatedDate(existing.getCreatedDate());
      }
      entity.setUpdatedBy(currentUserId);
      entity.setUpdatedDate(now);

      entities.add(entity);
    }

    requestItemAtcRepository.saveAll(entities);
  }

  @Override
  public List<DrugLabelDTO> getDrugLabelByItemSeqno(Long itemSeqno) {
    return requestRepositoryJooq.getDrugLabelListByItemSeqno(itemSeqno);
  }

  @Override
  public List<DrugLabelDTO> getAllDrugLabel() {
    List<RequestDrugLabel> list = requestDrugLabelRepository.findAll();
    return list.stream()
        .map(
            entity -> {
              return new DrugLabelDTO(
                  entity.getDrugReqSeqno(),
                  entity.getItemSeqno(),
                  entity.getIndicationCode(),
                  entity.getIndicationDescription(),
                  entity.getGuideline(),
                  entity.getStatus());
            })
        .collect(Collectors.toList());
  }

  @Override
  @Transactional
  public void saveAllDrugLabel(Long itemSeqno, List<DrugLabelDTO> dtoList) {
    List<RequestDrugLabel> entities = new ArrayList<>();
    Long currentUserId = CurrentUserUtil.getCurrentUserId();
    LocalDateTime now = LocalDateTime.now();

    for (DrugLabelDTO dto : dtoList) {
      RequestDrugLabel entity = new RequestDrugLabel();

      entity.setDrugReqSeqno(dto.drugReqSeqno());
      entity.setItemSeqno(itemSeqno);
      entity.setIndicationCode(dto.indicationCode());
      entity.setIndicationDescription(dto.indicationDescription());
      entity.setGuideline(dto.guideline());
      entity.setStatus(dto.status());

      if (dto.drugReqSeqno() == null) {
        entity.setCreatedBy(currentUserId);
        entity.setCreatedDate(now);
        entity.setActiveFlag('A');
      } else {
        RequestDrugLabel existing =
            requestDrugLabelRepository
                .findById(dto.drugReqSeqno())
                .orElseThrow(
                    () ->
                        new RmsException(
                            ExceptionCode.ERROR, "ATC not found with seqno " + dto.drugReqSeqno()));

        entity.setCreatedBy(existing.getCreatedBy());
        entity.setCreatedDate(existing.getCreatedDate());
      }
      entity.setUpdatedBy(currentUserId);
      entity.setUpdatedDate(now);

      entities.add(entity);
    }

    requestDrugLabelRepository.saveAll(entities);
  }

  @Override
  @Transactional
  public void updateItemMasterMethodStatus(ItemMasterMethodStatusDTO dto) {
    saveItemMaster(dto.itemMaster());
    savePackaging(dto.itemReqSeqno(), dto.packagingList());
    saveFrequency(dto.itemReqSeqno(), dto.frequencyList());
    saveIndication(dto.itemReqSeqno(), dto.indicationList());
    saveDrugLabel(dto.itemReqSeqno(), dto.drugLabelList());

    String status = "Draft";
    if ("confirm".equalsIgnoreCase(dto.actionType())) {
      status = "Confirmed";
    }
    saveItemMasterMethodStatus(dto.itemReqSeqno(), status, dto.actionType());
  }

  private void saveItemMaster(AdminItemMasterDetailDTO dto) {
    RequestItemMaster entity = new RequestItemMaster();
    BeanUtils.copyProperties(dto, entity);
    requestItemMasterRepository.save(entity);
  }

  private void savePackaging(Long itemSeqno, List<AdminItemPackagingDTO> dtoList) {
    if (dtoList == null) {
      return;
    }
    List<RequestItemPackaging> entities = new ArrayList<>();

    for (AdminItemPackagingDTO dto : dtoList) {
      RequestItemPackaging entity = new RequestItemPackaging();
      BeanUtils.copyProperties(dto, entity);
      entity.setItemSeqno(itemSeqno);
      entities.add(entity);
    }
    requestItemPackagingRepository.saveAll(entities);
  }

  private void saveFrequency(Long itemSeqno, List<RequestItemFrequencyDTO> dtoList) {
    if (dtoList == null) {
      return;
    }
    List<RequestItemFrequency> entities = new ArrayList<>();

    for (RequestItemFrequencyDTO dto : dtoList) {
      RequestItemFrequency entity = new RequestItemFrequency();
      BeanUtils.copyProperties(dto, entity);
      entity.setItemSeqno(itemSeqno);
      entities.add(entity);
    }
    requestItemFrequencyRepository.saveAll(entities);
  }

  private void saveIndication(Long itemSeqno, List<RequestItemAtcDTO> dtoList) {
    if (dtoList == null) {
      return;
    }
    List<RequestItemAtc> entities = new ArrayList<>();

    for (RequestItemAtcDTO dto : dtoList) {
      RequestItemAtc entity = new RequestItemAtc();
      BeanUtils.copyProperties(dto, entity);
      entity.setItemSeqno(itemSeqno);
      entities.add(entity);
    }
    requestItemAtcRepository.saveAll(entities);
  }

  private void saveDrugLabel(Long itemSeqno, List<DrugLabelDTO> dtoList) {
    if (dtoList == null) {
      return;
    }
    List<RequestDrugLabel> entities = new ArrayList<>();

    for (DrugLabelDTO dto : dtoList) {
      RequestDrugLabel entity = new RequestDrugLabel();
      BeanUtils.copyProperties(dto, entity);
      entity.setItemSeqno(itemSeqno);
      entities.add(entity);
    }
    requestDrugLabelRepository.saveAll(entities);
  }

  private void saveItemMasterMethodStatus(Long itemReqSeqno, String status, String actionType) {
    if ("confirm".equalsIgnoreCase(actionType)) {
      requestItemMasterRepository.updateMethodStatus(itemReqSeqno, status);
    }
  }
}
